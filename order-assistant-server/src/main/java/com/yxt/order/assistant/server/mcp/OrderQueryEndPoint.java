package com.yxt.order.assistant.server.mcp;


import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.mcp.req.OrderTypeQueryReq;
import com.yxt.order.atom.sdk.mcp.res.OrderTypeQueryRes;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 订单查询端点
 */
public interface OrderQueryEndPoint {

  String BASE_PATH = "/mcp/query";

  /**
   * 查询订单类型
   *
   * @param orderTypeReq
   * @return
   */
  @PostMapping(BASE_PATH + "/order-type")
  ResponseBase<OrderTypeQueryRes> queryOrderType(@RequestBody OrderTypeQueryReq orderTypeReq);


}
