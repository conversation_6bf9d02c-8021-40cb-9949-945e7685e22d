//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.yxt.order.assistant.server.knowledge;


import com.yxt.lang.constants.response.ResponseCodeType;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lang.util.DateHelper;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.BindingResult;

public abstract class AbstractController {
  @Value("${api.version:1.0}")
  private String API_VERSION;
  @Value("${api.base-info-version:1.0}")
  private String API_BASE_INFO_VERSION;

  protected void checkValid(BindingResult result) {
    if (result.hasErrors()) {
      String message = result.getFieldError().getDefaultMessage();
      throw new YxtBizException(message);
    }
  }

  protected <T> ResponseBase<T> generateSuccess(T tObject) {
    ResponseBase<T> base = ResponseBase.success();
    base.setData(tObject);
    return base;
  }

  protected <T> ResponseBase<T> generateObjectSuccess(T tObject) {
    ResponseBase<T> base = ResponseBase.success();
    base.setData(tObject);
    return base;
  }

  protected ResponseBase generateBitSuccess(boolean res) {
    return res ? ResponseBase.success() : ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION);
  }

  protected ResponseBase generateLineSuccess(int line, int size) {
    if (size == 0) {
      return line > size ? ResponseBase.success() : ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION);
    } else {
      return line == size ? ResponseBase.success() : ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION);
    }
  }

  protected <T> ResponseBase<PageDTO<T>> generatePageDtoSuccess(Long total, List<T> list) {
    ResponseBase<PageDTO<T>> base = ResponseBase.success();
    PageDTO<T> pageDTO = new PageDTO();
    pageDTO.setTotalCount(total);
    pageDTO.setData(list);
    base.setData(pageDTO);
    return base;
  }

  protected <T> ResponseBase<T> generateError(ResponseCodeType type) {
    return ResponseBase.fail(type);
  }



  public String getAPI_VERSION() {
    return this.API_VERSION;
  }

  public String getAPI_BASE_INFO_VERSION() {
    return this.API_BASE_INFO_VERSION;
  }

  public void setAPI_VERSION(final String API_VERSION) {
    this.API_VERSION = API_VERSION;
  }

  public void setAPI_BASE_INFO_VERSION(final String API_BASE_INFO_VERSION) {
    this.API_BASE_INFO_VERSION = API_BASE_INFO_VERSION;
  }

  public boolean equals(final Object o) {
    if (o == this) {
      return true;
    } else if (!(o instanceof AbstractController)) {
      return false;
    } else {
      AbstractController other = (AbstractController)o;
      if (!other.canEqual(this)) {
        return false;
      } else {
        Object this$API_VERSION = this.getAPI_VERSION();
        Object other$API_VERSION = other.getAPI_VERSION();
        if (this$API_VERSION == null) {
          if (other$API_VERSION != null) {
            return false;
          }
        } else if (!this$API_VERSION.equals(other$API_VERSION)) {
          return false;
        }

        Object this$API_BASE_INFO_VERSION = this.getAPI_BASE_INFO_VERSION();
        Object other$API_BASE_INFO_VERSION = other.getAPI_BASE_INFO_VERSION();
        if (this$API_BASE_INFO_VERSION == null) {
          if (other$API_BASE_INFO_VERSION != null) {
            return false;
          }
        } else if (!this$API_BASE_INFO_VERSION.equals(other$API_BASE_INFO_VERSION)) {
          return false;
        }

        return true;
      }
    }
  }

  protected boolean canEqual(final Object other) {
    return other instanceof AbstractController;
  }

  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    Object $API_VERSION = this.getAPI_VERSION();
    result = result * 59 + ($API_VERSION == null ? 43 : $API_VERSION.hashCode());
    Object $API_BASE_INFO_VERSION = this.getAPI_BASE_INFO_VERSION();
    result = result * 59 + ($API_BASE_INFO_VERSION == null ? 43 : $API_BASE_INFO_VERSION.hashCode());
    return result;
  }

  public String toString() {
    return "AbstractController(API_VERSION=" + this.getAPI_VERSION() + ", API_BASE_INFO_VERSION=" + this.getAPI_BASE_INFO_VERSION() + ")";
  }
}
