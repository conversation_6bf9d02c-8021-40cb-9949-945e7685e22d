package com.yxt.order.assistant.server.knowledge.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.assistant.server.config.OrderAssistantConfig;
import com.yxt.order.assistant.server.dify.enhance.SegmentationEnhance;
import com.yxt.order.assistant.server.knowledge.confluence.ConfluenceMarkdownExporter;
import com.yxt.order.assistant.server.knowledge.database.DatabaseSchemaService;
import com.yxt.order.assistant.server.knowledge.database.TableNameMerger;
import com.yxt.order.assistant.server.knowledge.database.TableNameMerger.TableGroup;
import com.yxt.order.assistant.server.knowledge.database.dto.DatabaseTableInfo;
import com.yxt.order.assistant.server.knowledge.req.CreateKnowledgeBaseReq;
import com.yxt.order.assistant.server.knowledge.req.DeleteKnowledgeBaseDocumentReq;
import com.yxt.order.assistant.server.knowledge.req.UploadKnowledgeBaseDocumentReq;
import com.yxt.order.assistant.server.knowledge.swagger.SwaggerMarkdownExporter;
import com.yxt.order.assistant.server.knowledge.swagger.dto.InterfaceInfo;
import com.yxt.order.assistant.server.repository.KnowledgeBaseRepository;
import com.yxt.order.assistant.server.repository.KnowledgeRepository;
import com.yxt.order.assistant.server.repository.entity.Knowledge;
import com.yxt.order.assistant.server.repository.entity.KnowledgeBase;
import com.yxt.order.assistant.server.repository.entity.KnowledgeBase.CfConfig;
import com.yxt.order.assistant.server.repository.entity.KnowledgeBase.DataSet;
import com.yxt.order.assistant.server.repository.entity.KnowledgeBase.SwaggerConfig;
import com.yxt.order.assistant.server.repository.enums.KnowledgeBaseSource;
import com.yxt.order.assistant.server.repository.enums.KnowledgeBaseStatus;
import com.yxt.order.assistant.server.repository.enums.KnowledgeTargetStatus;
import com.yxt.order.assistant.server.repository.enums.KnowledgeUploadStatus;
import io.github.imfangs.dify.client.DifyDatasetsClient;
import io.github.imfangs.dify.client.exception.DifyApiException;
import io.github.imfangs.dify.client.model.datasets.CreateDatasetRequest;
import io.github.imfangs.dify.client.model.datasets.CreateDocumentByTextRequest;
import io.github.imfangs.dify.client.model.datasets.DatasetResponse;
import io.github.imfangs.dify.client.model.datasets.DocumentResponse;
import io.github.imfangs.dify.client.model.datasets.ProcessRule;
import io.github.imfangs.dify.client.model.datasets.ProcessRule.PreProcessingRule;
import io.github.imfangs.dify.client.model.datasets.ProcessRule.Rules;
import io.github.imfangs.dify.client.model.datasets.ProcessRule.SubchunkSegmentation;
import io.github.imfangs.dify.client.model.datasets.RetrievalModel;
import io.github.imfangs.dify.client.model.datasets.UpdateDocumentByTextRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class KnowledgeServiceImpl implements KnowledgeService {

  @Resource
  private DifyDatasetsClient difyDatasetsClient;

  @Resource
  private ConfluenceMarkdownExporter confluenceMarkdownExporter;

  @Resource
  private SwaggerMarkdownExporter swaggerMarkdownExporter;

  @Resource
  private DatabaseSchemaService databaseSchemaService;


  @Resource
  private KnowledgeBaseRepository knowledgeBaseRepository;

  @Resource
  private KnowledgeRepository knowledgeRepository;

  @Resource
  private OrderAssistantConfig orderAssistantConfig;

  @Override
  public KnowledgeBase detail(Long id) {
    LambdaQueryWrapper<KnowledgeBase> query = new LambdaQueryWrapper<>();
    query.eq(KnowledgeBase::getId, id);
    KnowledgeBase knowledgeBase = knowledgeBaseRepository.selectOne(query);
    if (knowledgeBase == null) {
      throw new RuntimeException("知识库不存在");
    }
    return knowledgeBase;
  }

  @Override
  public void pullCfKnowledge(KnowledgeBase knowledgeBase) {
    CfConfig cfConfig = knowledgeBase.fetchCfExtendJson();
    confluenceMarkdownExporter.pullPageAndChildrenToMarkdown(knowledgeBase.getId(), cfConfig);
  }

  @Override
  public void pullSwaggerKnowledge(Long knowledgeBaseId) {

    KnowledgeBase knowledgeBase = detail(knowledgeBaseId);

    SwaggerConfig swaggerConfig = knowledgeBase.fetchSwaggerFromJson();

    for (String swaggerUrl : swaggerConfig.getSwaggerUrlList()) {
      List<InterfaceInfo> interfaceList = swaggerMarkdownExporter.exportSwaggerToMarkdown(swaggerUrl);
      if(CollectionUtils.isEmpty(interfaceList)){
        log.error("未获取到Swagger接口信息");
        continue;
      }

      interfaceList.forEach(interfaceInfo -> {
        String title = interfaceInfo.uniqueId();
        String targetId = interfaceInfo.uniqueId();
        String markdown = interfaceInfo.getMarkdown();

        insertOrUpdate(title, targetId, markdown, knowledgeBaseId, KnowledgeBaseSource.SWAGGER);
      });
    }


  }


  @Override
  public void pullDatabaseKnowledge(KnowledgeBase knowledgeBase) {
    Long knowledgeBaseId = knowledgeBase.getId();
    log.info("开始拉取数据库表信息到知识库: {}", knowledgeBase.getName());

    List<DatabaseTableInfo> tableInfoList = databaseSchemaService.getTablesInfo(knowledgeBase);

    Map<String, List<DatabaseTableInfo>> collect = tableInfoList.stream()
        .collect(Collectors.groupingBy(DatabaseTableInfo::getSchemaName));

    // 获取有用的分表
    collect.forEach((schemaName, tables) -> {
      // 获取有效的表结构
      List<DatabaseTableInfo> availableTableList = getAvailableTableList(schemaName, tables);
      // 按照表的维度生成知识库文档
      for (DatabaseTableInfo databaseTableInfo : availableTableList) {

        String originSql = databaseTableInfo.getOriginSql(); // 原始Sql召回效果好

        if (!StringUtils.hasText(originSql)) {
          log.warn("数据库表信息导出为空");
          continue;
        }
        String target = String.format("%s.%s", schemaName, databaseTableInfo.getTableName());

        final String title = target;
        final String targetId = target;
        insertOrUpdate(title, targetId, originSql, knowledgeBaseId, KnowledgeBaseSource.DATABASE);
      }

    });


  }

  @Override
  public void insertOrUpdate(String title, String targetId, String content, Long knowledgeBaseId,
      KnowledgeBaseSource source) {
    // 创建知识条目
    Knowledge knowledge = new Knowledge();
    knowledge.setSource(source.name());
    knowledge.setTargetName(title);
    knowledge.setTargetId(targetId);
    knowledge.setTargetStatus(KnowledgeTargetStatus.NORMAL.name());
    knowledge.setContent(content);
    knowledge.setKnowledgeBaseId(knowledgeBaseId);
    knowledge.setUploadStatus(KnowledgeUploadStatus.WAIT.name());

    // 检查是否已存在相同的知识条目
    LambdaQueryWrapper<Knowledge> query = new LambdaQueryWrapper<>();
    query.eq(Knowledge::getKnowledgeBaseId, knowledgeBaseId).eq(Knowledge::getSource, source.name())
        .eq(Knowledge::getTargetId, targetId);

    Knowledge existingKnowledge = knowledgeRepository.selectOne(query);

    if (existingKnowledge != null) {
      // 更新现有记录
      existingKnowledge.setContent(content);
      existingKnowledge.setTargetName(title);
      existingKnowledge.setUploadStatus(KnowledgeUploadStatus.SUCCESS.name());
      knowledgeRepository.updateById(existingKnowledge);
      log.info("更新数据库表结构文档成功: {}", existingKnowledge.getId());
    } else {
      // 插入新记录
      int insert = knowledgeRepository.insert(knowledge);
      if (insert > 0) {
        log.info("新增数据库表结构文档成功: {}", knowledge.getId());
      }
    }
  }

  private List<DatabaseTableInfo> getAvailableTableList(String schemaName,
      List<DatabaseTableInfo> tables) {
    if (orderAssistantConfig.isNeedMergeTable(schemaName)) {
      // 表名按照有数字和没有数字分组
      TableGroup tableGroup = TableNameMerger.tableNameGroup(tables);

      // 获取配置的分表序列
      String databaseSelectSeq = orderAssistantConfig.getDatabaseSelectSeq(schemaName);

      List<DatabaseTableInfo> seqTables = tableGroup.getNumericTableNames().stream()
          .filter(s -> s.getTableName().endsWith(databaseSelectSeq)).toList();

      // 处理原始Sql中的序列
      seqTables = seqTables.stream().map(seqTable -> {
        String originSql = seqTable.getOriginSql();
        String cleanSql = originSql.replaceAll("_"+databaseSelectSeq, Strings.EMPTY);
        seqTable.setOriginSql(cleanSql);

        String tableName = seqTable.getTableName();
        seqTable.setTableName(tableName.replaceAll("_"+databaseSelectSeq, Strings.EMPTY));
        return seqTable;
      }).toList();

      List<DatabaseTableInfo> allTableList = Lists.newArrayList();
      allTableList.addAll(seqTables);
      allTableList.addAll(tableGroup.getCommonTableNames());

      for (DatabaseTableInfo databaseTableInfo : allTableList) {
        String cleanTableName = TableNameMerger.fetchCleanTable(databaseTableInfo.getTableName());
        databaseTableInfo.setTableName(cleanTableName);
      }
      return allTableList;
    } else {
      return tables;

    }
  }

  @Override
  public void upload(UploadKnowledgeBaseDocumentReq req) {
    Long knowledgeBaseId = req.getKnowledgeBaseId();
    KnowledgeBase detail = detail(knowledgeBaseId);
    DataSet dataSet = detail.fetchDataSet();
    String dataSetId = dataSet.getDataSetId();
    Asserts.notEmpty(dataSetId, "dataSetId不能为空");

    LambdaQueryWrapper<Knowledge> query = new LambdaQueryWrapper<>();
    query.eq(Knowledge::getKnowledgeBaseId, detail.getId());
    List<Knowledge> knowledgeList = knowledgeRepository.selectList(query);
    if (CollectionUtils.isEmpty(knowledgeList)) {
      log.info("知识库内容为空,不需要上传");
      return;
    }

    for (Knowledge knowledge : knowledgeList) {
      String mappingRemoteId = knowledge.getMappingRemoteId();
      try {
        if (StringUtils.isEmpty(mappingRemoteId)) {
          addDocument(knowledge, dataSetId);
          log.info("knowledge:id:{} 新增文档成功", knowledge.getId());
        } else {
          updateDocument(knowledge, dataSetId, mappingRemoteId);
          log.info("knowledge:id:{} 更新文档成功", knowledge.getId());
        }

        knowledge.setUploadStatus(KnowledgeUploadStatus.SUCCESS.name());
        knowledge.setError(Strings.EMPTY);
      } catch (Exception e) {
        knowledge.setUploadStatus(KnowledgeUploadStatus.FAIL.name());
        knowledge.setError(e.getMessage());
        e.printStackTrace();
      }
      knowledgeRepository.updateById(knowledge);
    }
  }


  private void updateDocument(Knowledge knowledge, String dataSetId, String mappingRemoteId)
       {
    try {
      UpdateDocumentByTextRequest request = UpdateDocumentByTextRequest.builder()
          .name(knowledge.getTargetName()).text(knowledge.getContent()).processRule(sameRule())
          .build();
      difyDatasetsClient.updateDocumentByText(dataSetId, mappingRemoteId, request);
    } catch (Exception e) {

      try {
        // 先删除
        difyDatasetsClient.deleteDocument(dataSetId, knowledge.getMappingRemoteId());

        // 再新增
        addDocument(knowledge,dataSetId);
      } catch (Exception ex) {
        log.error("已经尝试先删除,再新增了,依然异常",ex);
      }

    }
  }

  public ProcessRule sameRule() {

    SegmentationEnhance enhance = new SegmentationEnhance();
    enhance.setMaxTokens(4000);
    enhance.setChunkOverlap((int) (enhance.getMaxTokens() * 0.25));
    enhance.setSeparator(orderAssistantConfig.getDefaultSeparator());

    return ProcessRule.builder().mode("hierarchical").rules(Rules.builder().preProcessingRules(
            Lists.newArrayList(
                PreProcessingRule.builder().id("remove_extra_spaces").enabled(true).build()))
        .segmentation(enhance).subchunkSegmentation(
            SubchunkSegmentation.builder().maxTokens(enhance.getMaxTokens())
                .separator(enhance.getSeparator()).chunkOverlap(enhance.getChunkOverlap()).build())
        .build()).build();
  }


  private void addDocument(Knowledge knowledge, String dataSetId)
      throws IOException, DifyApiException {
    RetrievalModel retrievalModel = new RetrievalModel();
    retrievalModel.setSearchMethod("hybrid_search");
    retrievalModel.setRerankingEnable(false);
    retrievalModel.setTopK(10);
    retrievalModel.setScoreThresholdEnabled(false);

    CreateDocumentByTextRequest request = CreateDocumentByTextRequest.builder()
        .name(knowledge.getTargetName()).text(knowledge.getContent())
        .indexingTechnique("high_quality").docForm("text_model")
        // 1.1.3 invalid_param (400) - Must not be null! 【doc_language】
        .docLanguage("Chinese")
        // 1.1.3 invalid_param (400) - Must not be null! 【retrieval_model】
        .retrievalModel(retrievalModel)
        // 没有这里的设置，会500报错，服务器内部错误
        .processRule(sameRule()).build();

    DocumentResponse documentByText = difyDatasetsClient.createDocumentByText(dataSetId, request);
    // 如果在拉取的时候没有处理好 mappingRemoteId,这里在创建的时候维护
    String id = documentByText.getDocument().getId();
    knowledge.setMappingRemoteId(id);
  }

  @Override
  public void delete(DeleteKnowledgeBaseDocumentReq req) {
    Long knowledgeBaseId = req.getKnowledgeBaseId();
    KnowledgeBase detail = detail(knowledgeBaseId);
    DataSet dataSet = detail.fetchDataSet();
    String dataSetId = dataSet.getDataSetId();
    Asserts.notEmpty(dataSetId, "dataSetId不能为空");

    LambdaQueryWrapper<Knowledge> query = new LambdaQueryWrapper<>();
    query.eq(Knowledge::getKnowledgeBaseId, detail.getId());
    List<Knowledge> knowledgeList = knowledgeRepository.selectList(query);
    if (CollectionUtils.isEmpty(knowledgeList)) {
      log.info("知识库内容为空");
      return;
    }

    for (Knowledge knowledge : knowledgeList) {
      try {
        difyDatasetsClient.deleteDocument(dataSetId, knowledge.getMappingRemoteId());
        knowledgeRepository.deleteById(knowledge.getId());
        log.info("知识删除:{} , 标题: {}", knowledge.getId(), knowledge.getTargetName());
      } catch (Exception e) {
        knowledge.setError(e.getMessage());
      }

      knowledgeRepository.updateById(knowledge);
    }
  }

  @Override
  public void create(CreateKnowledgeBaseReq req) {

    LambdaQueryWrapper<KnowledgeBase> exist = new LambdaQueryWrapper<>();
    exist.eq(KnowledgeBase::getName, req.getName());
    if (knowledgeBaseRepository.selectCount(exist) >= 1) {
      throw new RuntimeException("知识库名称已存在");
    }

    CreateDatasetRequest request = new CreateDatasetRequest();
    request.setName(req.getName());
    request.setDescription(req.getDescription());
    request.setIndexingTechnique("high_quality");
    request.setPermission("all_team_members");
    request.setProvider("vendor");
//    request.setExternalKnowledgeApiId();
//    request.setExternalKnowledgeId();
    DataSet dataSet = new DataSet();
    try {
      DatasetResponse datasetResponse = difyDatasetsClient.createDataset(request);
      dataSet.setDataSetId(datasetResponse.getId());
      log.info("创建知识库成功,{}", dataSet.getDataSetId());

      KnowledgeBase knowledgeBase = new KnowledgeBase();
      knowledgeBase.setSource(req.getSource().name());
      knowledgeBase.setName(req.getName());
      knowledgeBase.setMappingDifyConfig(JsonUtils.toJson(dataSet));
      knowledgeBase.setStatus(KnowledgeBaseStatus.NORMAL.name());

      int insert = knowledgeBaseRepository.insert(knowledgeBase);
      if (insert <= 0) {
        throw new RuntimeException("知识库入库失败");
      }
    } catch (Exception e) {
      if (!StringUtils.isEmpty(dataSet.getDataSetId())) {
        try {
          difyDatasetsClient.deleteDataset(dataSet.getDataSetId());
          log.info("创建知识库成功,但后续逻辑异常,删除知识库,{}", dataSet.getDataSetId());
        } catch (Exception ex) {
          throw new RuntimeException("创建知识库成功,但后续逻辑异常,删除知识库", ex);
        }
      }

      throw new RuntimeException("创建知识库失败", e);
    }

  }
}
