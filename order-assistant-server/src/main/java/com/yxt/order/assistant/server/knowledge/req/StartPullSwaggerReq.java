package com.yxt.order.assistant.server.knowledge.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 开始拉取Swagger文档请求参数
 */
@ApiModel("开始拉取Swagger文档请求参数")
@Data
public class StartPullSwaggerReq {

    /**
     * 知识库组ID
     */
    @NotNull(message = "知识库组ID不能为空")
    @ApiModelProperty("知识库组ID")
    private Long knowledgeBaseId;


}
